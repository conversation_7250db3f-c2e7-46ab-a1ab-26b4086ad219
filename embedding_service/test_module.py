#!/usr/bin/env python3
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_structure():
    print("🧪 测试embedding_service基础结构...")

    # 测试配置
    try:
        from utils.config import get_settings
        settings = get_settings()
        print(f"✅ 配置加载成功: {settings.embedding_model}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

    # 测试模型定义
    try:
        from models.embedding import EmbeddingRequest, EmbeddingResponse
        request = EmbeddingRequest(text="Hello, world!")
        print(f"✅ 数据模型正常: {request.text}")
    except Exception as e:
        print(f"❌ 数据模型失败: {e}")
        return False

    # 测试API结构
    try:
        from api.main import app
        print("✅ API应用创建成功")
    except Exception as e:
        print(f"❌ API应用失败: {e}")
        return False

    print("✅ embedding_service 基础结构测试通过")
    return True

def test_mock_embedding():
    print("🧪 测试模拟向量化...")

    # 创建模拟向量化服务
    import time
    import random

    class MockEmbeddingService:
        def __init__(self):
            self.dimension = 384

        def embed_text(self, text: str):
            start_time = time.time()
            # 生成模拟向量
            embedding = [random.random() for _ in range(self.dimension)]
            processing_time = (time.time() - start_time) * 1000

            return {
                "embedding": embedding,
                "dimension": len(embedding),
                "processing_time": processing_time,
                "model": "mock-model"
            }

    service = MockEmbeddingService()
    result = service.embed_text("Hello, world!")

    print(f"✅ 模拟向量维度: {result['dimension']}")
    print(f"✅ 模拟处理时间: {result['processing_time']:.2f}ms")
    print("✅ 模拟向量化测试通过")

if __name__ == "__main__":
    print("=" * 50)
    print("    Embedding Service 基础测试")
    print("=" * 50)

    if test_basic_structure():
        test_mock_embedding()
        print("\n🎉 所有测试通过！可以开始启动服务了")
        print("\n下一步:")
        print("uvicorn api.main:app --reload --port 8001")
    else:
        print("\n❌ 基础结构测试失败，请检查代码")
