from fastapi import FastAP<PERSON>, Depends
from ..services.embedding_service import EmbeddingService
from ..models.embedding import EmbeddingRequest, EmbeddingResponse
from ..utils.config import get_settings, Settings

app = FastAPI(title="Embedding Service", version="1.0.0")

def get_embedding_service(settings: Settings = Depends(get_settings)) -> EmbeddingService:
    return EmbeddingService(settings)

@app.post("/api/v1/embed", response_model=EmbeddingResponse)
async def embed_text(
    request: EmbeddingRequest,
    service: EmbeddingService = Depends(get_embedding_service)
):
    return service.embed_text(request)

@app.get("/api/v1/health")
async def health_check():
    return {"status": "healthy", "service": "embedding_service"}
