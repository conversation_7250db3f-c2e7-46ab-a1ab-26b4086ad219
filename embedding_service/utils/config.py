from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 模型配置
    embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2"
    embedding_dimension: int = 384
    
    # Redis配置
    redis_url: str = "redis://localhost:6379"
    cache_ttl: int = 3600
    
    # API配置
    api_host: str = "0.0.0.0"
    api_port: int = 8001
    
    class Config:
        env_prefix = "EMBEDDING_"

def get_settings() -> Settings:
    return Settings()
