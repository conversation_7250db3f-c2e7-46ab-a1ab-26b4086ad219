from pydantic import BaseModel
from typing import List, Optional

class EmbeddingRequest(BaseModel):
    text: str
    model: Optional[str] = None

class EmbeddingResponse(BaseModel):
    embedding: List[float]
    dimension: int
    processing_time: float
    model: str

class BatchEmbeddingRequest(BaseModel):
    texts: List[str]
    model: Optional[str] = None

class BatchEmbeddingResponse(BaseModel):
    embeddings: List[List[float]]
    count: int
    processing_time: float
    model: str
