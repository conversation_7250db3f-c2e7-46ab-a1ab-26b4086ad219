from sentence_transformers import SentenceTransformer
import time
import numpy as np
from typing import List
from ..models.embedding import EmbeddingRequest, EmbeddingResponse
from ..utils.config import Settings

class EmbeddingService:
    def __init__(self, settings: Settings):
        self.settings = settings
        self.model = SentenceTransformer(settings.embedding_model)
    
    def embed_text(self, request: EmbeddingRequest) -> EmbeddingResponse:
        start_time = time.time()
        
        # 生成向量
        embedding = self.model.encode(request.text)
        
        processing_time = (time.time() - start_time) * 1000
        
        return EmbeddingResponse(
            embedding=embedding.tolist(),
            dimension=len(embedding),
            processing_time=processing_time,
            model=self.settings.embedding_model
        )
    
    def embed_batch(self, texts: List[str]) -> List[List[float]]:
        embeddings = self.model.encode(texts)
        return [emb.tolist() for emb in embeddings]
